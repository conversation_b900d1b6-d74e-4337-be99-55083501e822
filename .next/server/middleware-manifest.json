{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "c5e9598a58991263d615ad8e5aff8329", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4cb70b8d33cfd99f262b2c648d2933c53c8a6d6ee82a23db95e8e04f61c4fed7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a1a6e778204aeb9c820d7a180fc8ee603a08ebdc06ae4b9cb02ab863870e2519"}}}, "sortedMiddleware": ["/"], "functions": {}}